package com.udaan.orderform.cart.mocks

import com.udaan.constraint.models.dtos.LogChargeEntityType
import com.udaan.instrumentation.TelemetryScope
import com.udaan.order_mgt.models.ModelV1
import com.udaan.orderform.common.models.LogCharge
import com.udaan.orderform.common.models.LogChargePayer
import com.udaan.orderform.common.models.OrgUnitLogCharge
import com.udaan.orderform.common.providers.LogChargeProvider
import java.util.concurrent.CompletableFuture

class LogChargeProviderMock: LogChargeProvider {
    override suspend fun getLogCharges(
        orders: List<ModelV1.SellerOrder>,
        shipToOrgUnitId: String,
        orderTotalInPaise: Long,
        sellingPlatform: com.udaan.proto.models.ModelV1.SellingPlatform,
        applyDefaultLogCharge: Boolean,
        deliveryChargeCartIdentifier: String?
    ): CompletableFuture<LogCharge> {
        return TelemetryScope.future {
            LogCharge(
                deliveryChargesInPaise = 0L,
                logChargeEntityType = LogChargeEntityType.DEFAULT,
                itemLevelLogCharge = emptyList(),
                chargePayer = LogChargePayer.BUYER,
                isApplicable = false
            )
        }
    }

    override suspend fun getLogChargesForMultipleOrgUnits(
        orders: List<ModelV1.SellerOrder>,
        shipToOrgUnitIds: List<String>,
        orderTotalInPaise: Long,
        sellingPlatform: com.udaan.proto.models.ModelV1.SellingPlatform,
        applyDefaultLogCharge: Boolean
    ): CompletableFuture<List<OrgUnitLogCharge>> {
        return TelemetryScope.future {
            shipToOrgUnitIds.map {
                OrgUnitLogCharge(
                    orgUnitId = it,
                    LogCharge(
                        deliveryChargesInPaise = 0L,
                        logChargeEntityType = LogChargeEntityType.DEFAULT,
                        itemLevelLogCharge = emptyList(),
                        chargePayer = LogChargePayer.BUYER,
                        isApplicable = false
                    )
                )
            }
        }
    }
}