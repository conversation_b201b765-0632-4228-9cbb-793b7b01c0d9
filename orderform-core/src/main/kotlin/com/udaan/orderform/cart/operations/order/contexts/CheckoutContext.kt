package com.udaan.orderform.cart.operations.order.contexts

import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.order_mgt.events.model.PromiseInfoResponse
import com.udaan.order_mgt.models.ModelV1.SellerOrder
import com.udaan.order_mgt.models.pg.PaymentGatewayV1
import com.udaan.orderform.cart.common.models.OrderDeliveryChargeData
import com.udaan.orderform.cart.common.models.OrderLineDeliveryCharge
import com.udaan.orderform.cart.common.models.PlaceOrderPlan
import com.udaan.orderform.cart.context.OrderContext
import com.udaan.orderform.cart.db.models.ReservationInfo
import com.udaan.orderform.cart.models.dto.AdditionalData
import com.udaan.orderform.cart.models.dto.BaseCheckoutReq
import com.udaan.orderform.models.RewardLine
import com.udaan.orderservice.models.OrderDeliveryCharge

@Suppress("TooManyFunctions")
data class CheckoutContext<R : BaseCheckoutReq>(
    val request: R,
) : BaseCheckoutContext {
    private val orderContextsMap = mutableMapOf<String, OrderContext>()
    private var transactionContext: TransactionContext? = null
    private var placedOrders: List<SellerOrder> = emptyList()
    private var listingsMap: Map<String, TradeListing> = emptyMap()
    private var createdOrders: List<SellerOrder> = emptyList()
    private var placeOrderPlans: List<PlaceOrderPlan> = emptyList()
    private val reservationsInfo: MutableMap<String, List<ReservationInfo>> = mutableMapOf()
    private val originalOrderContextsMap: MutableMap<String, OrderContext> = mutableMapOf()
    private val promiseInfoMap: MutableMap<String, PromiseInfoResponse> = mutableMapOf()
    private var rewardLines: List<RewardLine>? = null
    private var orderDeliveryCharge: OrderDeliveryCharge? = null
    private var orderDeliveryChargeDataMap: Map<String, OrderDeliveryChargeData>? = emptyMap()
    private var orderLineDeliveryChargeDataMap: Map<String, List<OrderLineDeliveryCharge>>? = emptyMap()
    private var itemDeliverySlotMap: Map<String, String>? = emptyMap()
    private var pricingAuditIds: Map<String, String> = emptyMap()
    override suspend fun getOrderContexts(): Map<String, OrderContext> {
        return orderContextsMap
    }

    override suspend fun setOrderContexts(orderContextsMap: Map<String, OrderContext>) {
        this.orderContextsMap.clear()
        this.orderContextsMap.putAll(orderContextsMap)
    }

    internal fun addReservationsInfo(orderId: String, reservationsInfo: List<ReservationInfo>) {
        this.reservationsInfo[orderId] = reservationsInfo
    }

    internal fun getReservationsInfo(orderId: String): List<ReservationInfo> = reservationsInfo[orderId] ?: emptyList()

    internal fun getReservationsInfoMap(): Map<String, List<ReservationInfo>> = reservationsInfo

    override fun setOriginalOrderContexts(orderContextsMap: Map<String, OrderContext>) {
        this.originalOrderContextsMap.clear()
        this.originalOrderContextsMap.putAll(orderContextsMap)
    }

    override fun getOriginalOrdersContext() = originalOrderContextsMap

    internal fun setListingsMap(listingsMap: Map<String, TradeListing>) {
        this.listingsMap = listingsMap
    }

    internal fun getCreatedOrder(): List<SellerOrder> {
        return createdOrders
    }

    internal fun setCreatedOrder(orders: List<SellerOrder>) {
        this.createdOrders = orders
    }

    internal fun setPlaceOrderPlans(plans: List<PlaceOrderPlan>) {
        this.placeOrderPlans = plans
    }

    internal fun getPlaceOrderPlans(): List<PlaceOrderPlan> {
        return this.placeOrderPlans
    }

    internal fun setPlacedOrders(placedOrders: List<SellerOrder>) {
        this.placedOrders = placedOrders
    }

    override fun getUserRequest(): BaseCheckoutReq = request

    override fun useRewardCoins(): Boolean = request.useRewards()

    override fun getPaymentModes(): List<PaymentGatewayV1.PaymentInstrument> = request.getSelectedPaymentModes()

    override fun getBuyerId(): String = request.getBuyerId()

    override fun getToOrgUnitId(): String = request.getDeliveryOrgUnitId()

    override suspend fun getBaseOrders(): List<SellerOrder> =
        originalOrderContextsMap.values.mapNotNull { it.getOrder() }

    override fun getPlacedOrders(): List<SellerOrder> = this.placedOrders
    override fun getOrderPlans(): List<PlaceOrderPlan> = placeOrderPlans

    override fun getAdditionalData(): AdditionalData = request.getReqAdditionalData()

    override fun addPromiseInfo(orderId: String, promiseInfoResponse: PromiseInfoResponse) {
        this.promiseInfoMap[orderId] = promiseInfoResponse
    }

    fun getPromiseInfo(): Map<String, PromiseInfoResponse> {
        return promiseInfoMap
    }

    override fun getRewardLines(): List<RewardLine> = rewardLines ?: emptyList()

    override fun getCouponIds(): List<String> = request.getReqCouponIds()

    // Note: This is not used in case of direct checkout
    override fun getOrdersFromSplits(): List<SellerOrder> = emptyList()
    override suspend fun getOrdersWithTaxesFromSplits(): List<SellerOrder> = emptyList()

    override suspend fun getBuyerOrg(): com.udaan.proto.models.ModelV1.OrgAccount? =
        orderContextsMap.values.firstOrNull()?.getBuyerOrg()?.orgAccount

    override suspend fun getListingsMap(): Map<String, TradeListing> = listingsMap

    override fun setOrderDeliveryChargeDataMap(orderDeliveryChargeDataMap: Map<String, OrderDeliveryChargeData>) {
        this.orderDeliveryChargeDataMap = orderDeliveryChargeDataMap
    }

    override fun getOrderDeliveryChargeDataMap(): Map<String, OrderDeliveryChargeData>? {
        return this.orderDeliveryChargeDataMap
    }

    override fun setOrderLineDeliveryChargeDataMap(
        orderLineDeliveryChargeDataMap: Map<String, List<OrderLineDeliveryCharge>>
    ) {
        this.orderLineDeliveryChargeDataMap = orderLineDeliveryChargeDataMap
    }

    override fun getOrderLineDeliveryChargeDataMap(): Map<String, List<OrderLineDeliveryCharge>>? {
        return this.orderLineDeliveryChargeDataMap
    }

    override fun setOrderDeliveryCharge(orderDeliveryCharge: OrderDeliveryCharge) {
        this.orderDeliveryCharge = orderDeliveryCharge
    }

    override fun getOrderDeliveryCharge(): OrderDeliveryCharge? {
        return this.orderDeliveryCharge
    }

    override fun setItemLevelDeliverySlot(itemLevelDeliverySlot: Map<String, String>?) {
        this.itemDeliverySlotMap = itemLevelDeliverySlot
    }

    override fun getItemLevelDeliverySlot(): Map<String, String>? {
        return this.itemDeliverySlotMap
    }

    internal fun setTransactionContext(transactionContext: TransactionContext) {
        this.transactionContext = transactionContext
    }

    internal fun getTransactionContext(): TransactionContext? = transactionContext

    internal fun setRewardLines(rewardLines: List<RewardLine>) {
        this.rewardLines = rewardLines
    }

    internal fun setPricingAuditIdsMap(lidToPricingAuditIdsMap: Map<String, String>) {
        val auditIdMap = this.pricingAuditIds.toMutableMap()
        lidToPricingAuditIdsMap.forEach { (key, value) ->
            auditIdMap[key] = value
        }
        this.pricingAuditIds = auditIdMap
    }

    internal fun getPricingAuditIdForListing(listingId: String, salesUnitId: String): String? {
        return this.pricingAuditIds["$listingId:$salesUnitId"]
    }
}
