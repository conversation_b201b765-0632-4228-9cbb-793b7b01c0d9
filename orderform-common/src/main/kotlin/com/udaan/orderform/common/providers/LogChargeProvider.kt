package com.udaan.orderform.common.providers

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.constraint.client.ConstraintClient
import com.udaan.constraint.models.dtos.*
import com.udaan.instrumentation.TelemetryScope
import com.udaan.order_mgt.models.ModelV1
import com.udaan.orderform.common.models.LogCharge
import com.udaan.orderform.common.models.LogChargePayer
import com.udaan.orderform.common.models.OrgUnitLogCharge
import kotlinx.coroutines.Dispatchers
import java.util.concurrent.CompletableFuture
import kotlin.time.measureTimedValue
import com.udaan.orderform.common.executeAwaitWithEachTimeOutAndRetries

interface LogChargeProvider {
    suspend fun getLogCharges(
        orders: List<ModelV1.SellerOrder>,
        shipToOrgUnitId: String,
        orderTotalInPaise: Long,
        sellingPlatform: com.udaan.proto.models.ModelV1.SellingPlatform,
        applyDefaultLogCharge: Boolean = true,
        deliveryChargeCartIdentifier: String? = null
    ): CompletableFuture<LogCharge>

    suspend fun getLogChargesForMultipleOrgUnits(
        orders: List<ModelV1.SellerOrder>,
        shipToOrgUnitIds: List<String>,
        orderTotalInPaise: Long,
        sellingPlatform: com.udaan.proto.models.ModelV1.SellingPlatform,
        applyDefaultLogCharge: Boolean = true,
        deliveryChargeCartIdentifier: String? = null
    ): CompletableFuture<List<OrgUnitLogCharge>>
}

class LogChargeProviderImpl @Inject constructor(
    private val constraintsClient: ConstraintClient
): LogChargeProvider {

    companion object {
        private val logger by logger()
        private val DEFAULT_LOG_CHARGE_RESPONSE = LogCharge(
            deliveryChargesInPaise = 0L,
            logChargeEntityType = LogChargeEntityType.DEFAULT,
            itemLevelLogCharge = emptyList<ItemLevelDeliveryCharge>(),
            chargePayer = LogChargePayer.BUYER,
            isApplicable = false
        )
        private val DEFAULT_DELIVERY_CHARGE_RESPONSE = DeliveryChargeResponseDto(
            deliveryChargesInPaise = 0L,
            entityType = LogChargeEntityType.DEFAULT,
            chargePayer = com.udaan.constraint.models.dtos.LogChargePayer.BUYER,
            itemLevelDeliveryChargeList = emptyList<ItemLevelDeliveryCharge>(),
            isApplicable = false
        )
    }

    override suspend fun getLogCharges(
        orders: List<ModelV1.SellerOrder>,
        shipToOrgUnitId: String,
        orderTotalInPaise: Long,
        sellingPlatform: com.udaan.proto.models.ModelV1.SellingPlatform,
        applyDefaultLogCharge: Boolean
    ): CompletableFuture<LogCharge> {
        return TelemetryScope.future(Dispatchers.IO) {
            val (logCharge, duration) = measureTimedValue {
                val listingRequests = getListingRequests(orders)
                kotlin.runCatching {
                    val deliveryCharge = constraintsClient.calculateDeliveryCharges(
                        DeliveryChargeRequestDto(
                            platformId = sellingPlatform,
                            categoryGroupId = orders.first().extraData.categoryGroupId,
                            buyerOrgId = orders.first().buyerId,
                            orderTotalInPaise = orderTotalInPaise,
                            listingRequests = listingRequests,
                            sellerOrgId = orders.first().sellerId,
                            toOrgUnitId = shipToOrgUnitId,
                            additionalData = mapOf(AdditionalDataKey.APPLY_DEFAULT_LOG_CHARGE to applyDefaultLogCharge)
                        )
                    ).executeAwaitWithEachTimeOutAndRetries(eachTimeoutMillis = 5000, retries = 2)
                    if (deliveryCharge.isApplicable.not()) {
                        logger.error("Delivery charge not applicable. Response - $deliveryCharge")
                    }
                    val chargePayer =
                        deliveryCharge.chargePayer?.let { LogChargePayer.valueOf(it.name) } ?: LogChargePayer.BUYER
                    LogCharge(
                        deliveryChargesInPaise = deliveryCharge.deliveryChargesInPaise,
                        logChargeEntityType = deliveryCharge.entityType ?: LogChargeEntityType.DEFAULT,
                        referenceSlab = null,
                        itemLevelLogCharge = deliveryCharge.itemLevelDeliveryChargeList ?: emptyList(),
                        chargePayer = chargePayer,
                        isApplicable = deliveryCharge.isApplicable
                    )
                }.getOrElse { ex ->
                    logger.error("Exception while calling constraint service for log charges " +
                            "for orderIDs ${orders.map { it.orderId }} toOrgUnitId $shipToOrgUnitId " +
                            "platform ${sellingPlatform.name} => $ex")
                    DEFAULT_LOG_CHARGE_RESPONSE
                }
            }
            logger.info("Time taken to fetch log charge for orders ${orders.map { it.orderId }} is $duration ms")
            logCharge
        }
    }

    override suspend fun getLogChargesForMultipleOrgUnits(
        orders: List<ModelV1.SellerOrder>,
        shipToOrgUnitIds: List<String>,
        orderTotalInPaise: Long,
        sellingPlatform: com.udaan.proto.models.ModelV1.SellingPlatform,
        applyDefaultLogCharge: Boolean
    ): CompletableFuture<List<OrgUnitLogCharge>> {
        return TelemetryScope.future(Dispatchers.IO) {
            val (orgUnitLogCharges, duration) = measureTimedValue {
                val listingRequests = getListingRequests(orders)
                kotlin.runCatching {
                    val deliveryChargePerOrgUnit = constraintsClient.calculateDeliveryChargesForMultipleOrgUnits(
                        DeliveryChargeRequestWithMultipleOrgUnitsDto(
                            platformId = sellingPlatform,
                            categoryGroupId = orders.first().extraData.categoryGroupId,
                            buyerOrgId = orders.first().buyerId,
                            orderTotalInPaise = orderTotalInPaise,
                            listingRequests = listingRequests,
                            sellerOrgId = orders.first().sellerId,
                            toOrgUnitIds = shipToOrgUnitIds,
                            additionalData = mapOf(AdditionalDataKey.APPLY_DEFAULT_LOG_CHARGE to applyDefaultLogCharge)
                        )
                    ).executeAwaitWithEachTimeOutAndRetries(eachTimeoutMillis = 5000, retries = 2)
                    deliveryChargePerOrgUnit.map {
                        OrgUnitLogCharge(
                            orgUnitId = it.orgUnitId,
                            logCharge = LogCharge(
                                deliveryChargesInPaise = it.deliveryChargeResponseDto.deliveryChargesInPaise,
                                referenceSlab = null,
                                logChargeEntityType = LogChargeEntityType.DEFAULT,
                                itemLevelLogCharge = emptyList<ItemLevelDeliveryCharge>(),
                                chargePayer = LogChargePayer.BUYER,
                                isApplicable = it.deliveryChargeResponseDto.isApplicable
                            )
                        )
                    }

                }.getOrElse { ex ->
                    logger.error("Exception while calling constraint service for log charges " +
                            "for orderIDs ${orders.map { it.orderId }} toOrgUnitIds $shipToOrgUnitIds " +
                            "platform ${sellingPlatform.name} => $ex")
                    shipToOrgUnitIds.map {
                        OrgUnitLogCharge(
                            orgUnitId = it,
                            DEFAULT_LOG_CHARGE_RESPONSE
                        )
                    }
                }
            }
            logger.info("Time taken to fetch log charge for multiple orgUnits for orders " +
                "${orders.map { it.orderId }} is $duration ms")
            orgUnitLogCharges
        }
    }

    private fun getListingRequests(orders: List<ModelV1.SellerOrder>): List<ListingRequest> {
        val orderlineList = orders.map { it.orderLineList}.flatten()
        val orderLineLevies = orders.map { it.orderLineLevyList }.flatten()
        return orderlineList.map { orderLine ->
            val orderLineLevyLines = orderLineLevies.filter { it.orderLineId == orderLine.orderLineId }
            ListingRequest(
                listingId = orderLine.listingId,
                salesUnitId = orderLine.salesUnitId,
                quantity = orderLine.units,
                referenceId = orderLine.orderLineId,
                priceInPaise = orderLine.orderLineSpPaise + orderLineLevyLines.sumOf { it.levyAmountPaise }
            )
        }
    }
}